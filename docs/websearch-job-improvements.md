# WebSearchShopifyTLDLeadsJob Improvements

## Overview

This document outlines the improvements made to the `WebSearchShopifyTLDLeadsJob` (formerly `WebSearchCountryCodeLeadsJob`) to address the issues identified in the Laravel log analysis and to support comprehensive TLD-based Shopify store searches.

## Issues Fixed

### 1. TLD vs Country Code Confusion ✅
**Problem**: The job was filtering out TLDs like "COM", "store", "website" etc., treating them as invalid country codes.
- The job was designed for country code searches but users wanted to search TLDs
- Generic TLDs like `.store`, `.com`, `.website` were being filtered out
- This prevented searching for Shopify stores on these popular TLD extensions

**Solution**:
- Renamed job from `WebSearchCountryCodeLeadsJob` to `WebSearchShopifyTLDLeadsJob` to reflect its specific purpose of finding Shopify stores
- Removed filtering that blocked generic TLDs
- Now supports both country code TLDs (.dk, .se, .no) and generic TLDs (.com, .store, .website)
- Uses appropriate country codes for API calls while searching all TLD types

### 2. Timeout Errors ✅
**Problem**: Countries like CL, CN, GB were experiencing 30-second timeouts with no retry logic.

**Solution**:
- Implemented retry logic with exponential backoff
- Added configurable timeout settings (default: 60 seconds, minimum: 30 seconds)
- Added intelligent retry detection for timeout errors

### 3. No Error Recovery ✅
**Problem**: Failed requests were logged but not retried, leading to data loss.

**Solution**:
- Added retry mechanism with configurable attempts (default: 3 retries)
- Implemented exponential backoff with jitter to avoid thundering herd
- Added specific handling for different error types

### 4. Poor Error Categorization ✅
**Problem**: All errors were treated the same way.

**Solution**:
- Added error categorization (timeout, connection, rate_limit, invalid_country_code, etc.)
- Implemented smart retry logic that only retries recoverable errors
- Enhanced logging with error types for better debugging

## New Features

### Configurable Retry Parameters
```php
// Default configuration
WebSearchShopifyTLDLeadsJob::dispatch('week', ['bing', 'google']);

// Custom retry configuration
WebSearchShopifyTLDLeadsJob::dispatch(
    'week',                 // time range
    ['bing', 'google'],     // search engines
    5,                      // max retries (default: 3)
    3,                      // base delay seconds (default: 2)
    90                      // request timeout seconds (default: 60)
);
```

### Intelligent Error Handling

**Retryable Errors**:
- Timeout errors (cURL error 28, operation timed out)
- Connection errors (cURL error 7, network failures)
- Server errors (HTTP 5xx)
- Rate limiting (HTTP 429, rate limit exceeded)

**Non-Retryable Errors**:
- Invalid country codes (HTTP 400 with specific message)
- Authentication errors (HTTP 401, 403)
- Bad requests (HTTP 400)

### Enhanced Logging
```json
{
  "search_engines": ["bing", "google"],
  "time_range": "week",
  "max_retries": 3,
  "base_delay": 2,
  "request_timeout": 60,
  "environment": "production",
  "test_mode": false
}
```

### Country Code Validation
- Filters out known invalid codes: `com`, `eu`, `store`, `website`, etc.
- Validates 2-letter alphabetic codes
- Logs filtered codes for debugging

## Exponential Backoff Algorithm

```
delay = baseDelay * (2 ^ (attempt - 1))
jitter = delay * 0.25 * random(-1, 1)
finalDelay = min(delay + jitter, 60)
```

**Example delays with baseDelay=2**:
- Attempt 1: ~2 seconds (±25% jitter)
- Attempt 2: ~4 seconds (±25% jitter)
- Attempt 3: ~8 seconds (±25% jitter)
- Maximum: 60 seconds

## Testing

### New Test Coverage
- Retry logic validation
- Error categorization
- Exponential backoff calculation
- Country code filtering
- Configuration parameter validation

### Running Tests
```bash
# Test retry functionality
./vendor/bin/pest tests/Unit/WebSearchShopifyTLDLeadsJobRetryTest.php

# Test basic functionality
./vendor/bin/pest tests/Unit/WebSearchJobBasicTest.php

# Test all WebSearch functionality
./vendor/bin/pest tests/Unit/WebSearch*
```

## Usage Examples

### Basic Usage (Recommended)
```php
// Uses defaults: week timerange, 3 retries, 60s timeout
WebSearchShopifyTLDLeadsJob::dispatch();
```

### High-Reliability Configuration
```php
// For problematic regions with frequent timeouts
WebSearchShopifyTLDLeadsJob::dispatch(
    'week',                 // time range
    ['bing', 'google'],     // search engines
    5,                      // more retries
    3,                      // longer base delay
    120                     // longer timeout
);
```

### Fast Configuration
```php
// For testing or when speed is more important than reliability
WebSearchShopifyTLDLeadsJob::dispatch(
    'day',                  // shorter time range
    ['google'],             // single engine
    1,                      // fewer retries
    1,                      // shorter delay
    30                      // shorter timeout
);
```

## Expected Improvements

1. **TLD Support**: Now searches both country code TLDs (.dk, .se) and generic TLDs (.com, .store, .website)
2. **Better Recovery**: Timeout and connection errors are automatically retried
3. **Improved Reliability**: Exponential backoff prevents overwhelming APIs
4. **Better Debugging**: Enhanced logging with error categorization
5. **Configurable Behavior**: Adjust retry behavior based on requirements
6. **Broader Coverage**: Searches popular TLD extensions that were previously filtered out

## Monitoring

Watch for these log patterns to monitor improvements:

**Success after retry**:
```
[WebSearchShopifyTLDLeadsJob] Search succeeded for bing TLD cl on attempt 3
```

**TLD processing**:
```
[WebSearchShopifyTLDLeadsJob] Processing bing search for TLD: store with query: site:.store inurl:"/collections/all/" -site:shopify.com
```

**Retry attempts**:
```
[WebSearchShopifyTLDLeadsJob] Retryable error on attempt 2 for bing TLD cn. Retrying in 4 seconds.
```

The improvements should significantly reduce the errors seen in the original log file while maintaining the same functionality for collecting Shopify store leads.
