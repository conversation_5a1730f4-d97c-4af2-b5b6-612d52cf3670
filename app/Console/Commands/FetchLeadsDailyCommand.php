<?php

namespace App\Console\Commands;

use App\Jobs\FetchAdtractionJob;
use App\Jobs\FetchAwinJob;
use App\Jobs\FetchEmaerketLeadsJob;
use App\Jobs\FetchKelkooJob;
use App\Jobs\FetchMollyAppJob;
use App\Jobs\FetchPartnerAdsLeadsJob;
use App\Jobs\FetchViabillJob;
use App\Jobs\WebSearchShopifyTLDLeadsJob;
use Illuminate\Console\Command;

class FetchLeadsDailyCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cron:fetch-leads-daily';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        FetchEmaerketLeadsJob::dispatch();
        FetchViabillJob::dispatch();

        FetchMollyAppJob::dispatch();

        FetchPartnerAdsLeadsJob::dispatch();
        FetchAdtractionJob::dispatch();
        FetchAwinJob::dispatch();
        FetchKelkooJob::dispatch();

        // Web search for Shopify stores (Google, Bing)
        WebSearchShopifyTLDLeadsJob::dispatch('day');

        exit();

        // dnslytics Free Max 50
        // https://search.dnslytics.com/search?d=domains&q=*************

        // Free unlimited
        // https://viewdns.info/reverseip/?host=*************&t=1

        $manuel = [
            // Salecto
            '************',
            '************',

            // ShopOrama
            '*************',

            // HostedShop / DanDomain Webshop
            '.mywebshop.io',
            '************/24',

            '.hostedshop.dk',
            '*************',

            // Other from team.blue
            '************/24',

            // DanDomain Classic Webshop
            '************',
            '************',
            '************',
            '************',
            '************',
            '************',
            '************',
            '************',
            '************',
            '************',

            // Golden Planet
            // site:.gpdemo.com (DONT WORK)
            '***************',
            '**************',
            '***************',
            '***************',
            '***************',
            '*************',
            '**************',
            '**************',
            '**************',
            '**************',
            '**************',
            '**************',
            '*********',
            '**********',
            '**************',
            '**********',
            '********84',
            '*********7',
        ];
    }
}
