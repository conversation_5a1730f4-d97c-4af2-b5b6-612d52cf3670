<?php

namespace App\Console\Commands;

use App\Jobs\FetchShopifyTLDWebSearchLeadsJob;
use Illuminate\Console\Command;

class FetchLeadsWeeklyCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cron:fetch-leads-weekly';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        FetchShopifyTLDWebSearchLeadsJob::dispatch('week');
    }
}
