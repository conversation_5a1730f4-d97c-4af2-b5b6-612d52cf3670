<?php

namespace App\Jobs;

use App\Helpers\DomainParserHelper;
use App\Helpers\LeadBatchProcessorHelper;
use App\Helpers\WebSearch\BrightDataSerpHelper;
use App\Helpers\WebSearch\SerpApiHelper;
use App\Helpers\WebSearch\SerpStackHelper;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class WebSearchShopifyTLDLeadsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The search engines to use.
     */
    protected array $searchEngines;

    /**
     * The time range for search results.
     */
    protected string $timeRange;

    /**
     * Maximum number of retry attempts for failed requests.
     */
    protected int $maxRetries;

    /**
     * Base delay in seconds for exponential backoff.
     */
    protected int $baseDelay;

    /**
     * Timeout in seconds for API requests.
     */
    protected int $requestTimeout;

    /**
     * Create a new job instance.
     *
     * @param  string  $timeRange  The time range for search results (supported: 'day', 'week', 'month')
     *                             - 'day': Current day only
     *                             - 'week': Last 7 days (newest first) [DEFAULT]
     *                             - 'month': Last 30 days (newest first)
     * @param  array  $searchEngines  The search engines to use (supported: 'bing', 'google')
     *                                Note:
     *                                - 'bing' can use either SerpStackHelper or BrightDataSerpHelper (if configured)
     *                                - 'google' can use either SerpApiHelper or BrightDataSerpHelper (if configured)
     *                                All search engines are configured to return 100 results with newest results first.
     * @param  int  $maxRetries  Maximum number of retry attempts for failed requests (default: 3)
     * @param  int  $baseDelay  Base delay in seconds for exponential backoff (default: 2)
     * @param  int  $requestTimeout  Timeout in seconds for API requests (default: 60)
     */
    public function __construct(
        string $timeRange = 'week',
        array $searchEngines = ['bing', 'google'],
        int $maxRetries = 3,
        int $baseDelay = 2,
        int $requestTimeout = 60
    ) {
        $this->searchEngines = $searchEngines;
        $this->timeRange = $this->validateTimeRange($timeRange);
        $this->maxRetries = max(0, $maxRetries);
        $this->baseDelay = max(1, $baseDelay);
        $this->requestTimeout = max(30, $requestTimeout);
    }

    /**
     * Validate and normalize the time range parameter.
     *
     * @param  string  $timeRange  The time range to validate
     * @return string The validated time range
     */
    protected function validateTimeRange(string $timeRange): string
    {
        $validRanges = ['day', 'week', 'month'];
        $normalizedRange = strtolower(trim($timeRange));

        if (! in_array($normalizedRange, $validRanges)) {
            Log::warning('[WebSearchShopifyTLDLeadsJob] Invalid time range provided, defaulting to "week".', [
                'provided_range' => $timeRange,
                'valid_ranges' => $validRanges,
            ]);

            return 'week';
        }

        return $normalizedRange;
    }

    /**
     * Get time filter parameters for different search engines.
     *
     * @param  string  $engine  The search engine name
     * @return array The time filter parameters
     */
    protected function getTimeFilterParams(string $engine): array
    {
        switch ($engine) {
            case 'google':
                // Google uses 'tbs' parameter for time filtering
                switch ($this->timeRange) {
                    case 'week':
                        return ['tbs' => 'qdr:w']; // Last week (7 days)
                    case 'month':
                        return ['tbs' => 'qdr:m']; // Last month (30 days)
                    case 'day':
                    default:
                        return ['tbs' => 'qdr:d']; // Current day
                }

            case 'bing':
                // Bing uses 'time' parameter for time filtering
                switch ($this->timeRange) {
                    case 'week':
                        return ['time' => 'week']; // Last week (7 days)
                    case 'month':
                        return ['time' => 'month']; // Last month (30 days)
                    case 'day':
                    default:
                        return ['time' => 'day']; // Current day
                }

            default:
                return [];
        }
    }

    /**
     * Execute the job.
     */
    public function handle()
    {
        $environment = app()->environment();
        Log::info('[WebSearchShopifyTLDLeadsJob] Starting Shopify store search job.', [
            'search_engines' => $this->searchEngines,
            'time_range' => $this->timeRange,
            'max_retries' => $this->maxRetries,
            'base_delay' => $this->baseDelay,
            'request_timeout' => $this->requestTimeout,
            'environment' => $environment,
            'test_mode' => ($environment !== 'production'),
        ]);

        $allParsedLeads = [];
        $totalLeadsCollected = 0;

        $searchHelpers = $this->initializeSearchHelpers();
        if (empty($searchHelpers)) {
            Log::error('[WebSearchShopifyTLDLeadsJob] No search helpers could be initialized. Aborting job.');

            return;
        }

        foreach ($searchHelpers as $engineName => $helperConfig) {
            Log::info("[WebSearchShopifyTLDLeadsJob] Processing {$engineName} search with ".count($helperConfig['tlds']).' TLDs for Shopify stores');

            foreach ($helperConfig['tlds'] as $tld) {
                $query = 'site:.'.$tld.' inurl:"/collections/all/" -site:shopify.com -site:stackoverflow.com -site:help.shopify.com -site:accounts.shopify.com -site:shopify.dev -site:myshopify.com';

                Log::info("[WebSearchShopifyTLDLeadsJob] Processing {$engineName} search for TLD: {$tld} with query: {$query}");

                $params = $this->prepareSearchParams($helperConfig['search_params'], $tld);

                $results = $this->executeSearchWithRetry($helperConfig, $engineName, $query, $tld, $params);

                if (! empty($results)) {
                    $parsedResults = $this->processSearchResults($results, $engineName);

                    if (! empty($parsedResults)) {
                        $allParsedLeads = array_merge($allParsedLeads, $parsedResults);
                        $totalLeadsCollected += count($parsedResults);

                        Log::info("[WebSearchShopifyTLDLeadsJob] Collected {$totalLeadsCollected} Shopify store leads so far from {$engineName} for TLD: {$tld}");
                    }
                }

                sleep(1);
            }
        }

        if (! empty($allParsedLeads)) {
            Log::info('[WebSearchShopifyTLDLeadsJob] Passing '.count($allParsedLeads).' collected Shopify store leads to batch processor.');
            (new LeadBatchProcessorHelper)->processLeads($allParsedLeads);
        } else {
            Log::info('[WebSearchShopifyTLDLeadsJob] No Shopify store leads collected to process.');
        }

        Log::info("[WebSearchShopifyTLDLeadsJob] Shopify store search job finished. Total leads prepared for batching: {$totalLeadsCollected}");
    }

    /**
     * Initialize the search helpers for each enabled search engine.
     * This method configures TLDs (both country codes and generic TLDs) and search parameters for each engine.
     *
     * @return array The initialized search helpers with configuration
     *
     * @throws Exception
     */
    protected function initializeSearchHelpers(): array
    {
        $helpers = [];

        $brightDataConfig = [
            'api_key' => config('crawler.brightdata_api_key'),
            'customer_id' => config('crawler.brightdata_customer_id'),
            'zone_id' => config('crawler.brightdata_zone_id'),
        ];

        $brightDataConfigured = ! empty($brightDataConfig['api_key']) &&
            ! empty($brightDataConfig['customer_id']) &&
            ! empty($brightDataConfig['zone_id']);

        $brightDataHelper = null;
        if ($brightDataConfigured) {
            $brightDataHelper = new BrightDataSerpHelper(
                $brightDataConfig['api_key'],
                $brightDataConfig['customer_id'],
                $brightDataConfig['zone_id']
            );
            Log::info('[WebSearchShopifyTLDLeadsJob] Initialized BrightDataSerpHelper for Shopify store search');
        }

        // Include both country codes and generic TLDs for site: searches
        // Country codes are used for Google domain selection, TLDs are used for site: searches
        // Based on actual TLD usage data from Shopify store analysis
        $genericTlds = [
            // Most popular generic TLDs (high volume)
            'com', 'net', 'org', 'info', 'biz', 'eu',

            // E-commerce and business TLDs (medium-high volume)
            'store', 'shop', 'online', 'site', 'website', 'company',

            // Technology TLDs
            'tech', 'app', 'io', 'xyz', 'co',

            // Lifestyle and specialty TLDs (based on actual usage)
            'jewelry', 'bike', 'art', 'beer', 'clothing', 'studio', 'pet', 'fashion',
            'beauty', 'health', 'life', 'care', 'baby', 'watch', 'media', 'school',
            'audio', 'trade', 'pro', 'club', 'top', 'faith', 'direct', 'fit', 'cool',
            'dog', 'cards', 'global', 'world', 'run', 'one', 'shopping',

            // Geographic and cultural TLDs
            'barcelona', 'amsterdam', 'africa',

            // Other business and specialty TLDs
            'lighting', 'eco', 'tv', 'me'
        ];

        foreach ($this->searchEngines as $engine) {
            try {
                $engineName = strtolower($engine);
                $helperConfig = $this->createHelperConfig($engineName, $brightDataConfigured, $brightDataHelper, $genericTlds);

                if (! $helperConfig) {
                    continue;
                }

                $helpers[$engineName] = $helperConfig;

            } catch (Exception $e) {
                Log::error("[WebSearchShopifyTLDLeadsJob] Error initializing search helper for {$engine}: ".$e->getMessage());
            }
        }

        return $helpers;
    }

    /**
     * Create helper configuration for a specific search engine.
     *
     * @param  string  $engineName  The search engine name
     * @param  bool  $brightDataConfigured  Whether BrightData is configured
     * @param  BrightDataSerpHelper|null  $brightDataHelper  The BrightData helper instance
     * @param  array  $genericTlds  Generic TLDs to include
     * @return array|null The helper configuration or null if not available
     */
    protected function createHelperConfig(string $engineName, bool $brightDataConfigured, ?BrightDataSerpHelper $brightDataHelper, array $genericTlds): ?array
    {
        switch ($engineName) {
            case 'bing':
                if ($brightDataConfigured) {
                    return $this->createBingBrightDataConfig($brightDataHelper, $genericTlds);
                } elseif ($serpStackApiKey = config('crawler.serpstack_api_key')) {
                    return $this->createBingSerpStackConfig($serpStackApiKey, $genericTlds);
                } else {
                    Log::warning('[WebSearchShopifyTLDLeadsJob] No API keys configured for Bing search. Skipping.');

                    return null;
                }

            case 'google':
                if ($brightDataConfigured) {
                    return $this->createGoogleBrightDataConfig($brightDataHelper, $genericTlds);
                } elseif ($serpApiKey = config('crawler.serpapi_api_key')) {
                    return $this->createGoogleSerpApiConfig($serpApiKey, $genericTlds);
                } else {
                    Log::warning('[WebSearchShopifyTLDLeadsJob] No API keys configured for Google search. Skipping.');

                    return null;
                }

            default:
                Log::warning("[WebSearchShopifyTLDLeadsJob] Unsupported search engine: {$engineName}. Skipping.");

                return null;
        }
    }

    /**
     * Create Bing BrightData configuration.
     */
    protected function createBingBrightDataConfig(BrightDataSerpHelper $brightDataHelper, array $genericTlds): array
    {
        $tlds = $this->prepareTLDs(
            array_map('strtolower', BrightDataSerpHelper::SUPPORTED_COUNTRY_CODES),
            $genericTlds,
            'bing'
        );

        $searchParams = array_merge([
            'engine' => 'bing',
            'country_code' => '{COUNTRY_CODE}',
            'count' => 100,
        ], $this->getTimeFilterParams('bing'));

        Log::info('[WebSearchShopifyTLDLeadsJob] Using BrightDataSerpHelper for Bing Shopify store search');

        return [
            'class' => $brightDataHelper,
            'type' => 'brightdata',
            'engine' => 'bing',
            'tlds' => $tlds,
            'search_params' => $searchParams,
        ];
    }

    /**
     * Create Bing SerpStack configuration.
     */
    protected function createBingSerpStackConfig(string $serpStackApiKey, array $genericTlds): array
    {
        $tlds = $this->prepareTLDs(
            array_map('strtolower', SerpStackHelper::SUPPORTED_COUNTRY_CODES),
            $genericTlds,
            'bing'
        );

        $searchParams = array_merge([
            'engine' => 'bing',
            'country_code' => '{COUNTRY_CODE}',
            'num' => 100,
        ], $this->getTimeFilterParams('bing'));

        Log::info('[WebSearchShopifyTLDLeadsJob] Using SerpStackHelper for Bing Shopify store search');

        return [
            'class' => new SerpStackHelper($serpStackApiKey, true),
            'type' => 'serpstack',
            'engine' => 'bing',
            'tlds' => $tlds,
            'search_params' => $searchParams,
        ];
    }

    /**
     * Create Google BrightData configuration.
     */
    protected function createGoogleBrightDataConfig(BrightDataSerpHelper $brightDataHelper, array $genericTlds): array
    {
        $tlds = $this->prepareTLDs(
            array_map('strtolower', BrightDataSerpHelper::SUPPORTED_COUNTRY_CODES),
            $genericTlds,
            'google'
        );

        $searchParams = array_merge([
            'gl' => '{COUNTRY_CODE}',
            'num' => 100,
        ], $this->getTimeFilterParams('google'));

        Log::info('[WebSearchShopifyTLDLeadsJob] Using BrightDataSerpHelper for Google Shopify store search');

        return [
            'class' => $brightDataHelper,
            'type' => 'brightdata',
            'engine' => 'google',
            'tlds' => $tlds,
            'search_params' => $searchParams,
        ];
    }

    /**
     * Create Google SerpApi configuration.
     */
    protected function createGoogleSerpApiConfig(string $serpApiKey, array $genericTlds): array
    {
        $tlds = $this->prepareTLDs(
            array_map('strtolower', SerpApiHelper::SUPPORTED_COUNTRY_CODES),
            $genericTlds,
            'google'
        );

        $searchParams = array_merge([
            'gl' => '{COUNTRY_CODE}',
            'country_code' => '{COUNTRY_CODE}',
            'num' => 100,
        ], $this->getTimeFilterParams('google'));

        Log::info('[WebSearchShopifyTLDLeadsJob] Using SerpApiHelper for Google Shopify store search');

        return [
            'class' => new SerpApiHelper($serpApiKey),
            'type' => 'serpapi',
            'engine' => 'google',
            'tlds' => $tlds,
            'search_params' => $searchParams,
        ];
    }

    /**
     * Prepare TLDs by merging country codes with generic TLDs and applying environment filters.
     * For site: searches, we want to search both country code TLDs (.dk, .se) and generic TLDs (.store, .com).
     * Country codes are only used for Google domain selection (gl parameter).
     */
    protected function prepareTLDs(array $supportedCountryCodes, array $genericTlds, string $engineName): array
    {
        // Merge country codes and generic TLDs for comprehensive TLD search
        $allTlds = array_unique(array_merge($supportedCountryCodes, $genericTlds));
        sort($allTlds);

        if (app()->environment() !== 'production') {
            Log::info("[WebSearchShopifyTLDLeadsJob] Non-production environment detected. Using limited TLDs for {$engineName}.");
            // Use a mix of country code and generic TLD for testing
            return ['dk', 'store'];
        }

        Log::info("[WebSearchShopifyTLDLeadsJob] Prepared TLDs for {$engineName}.", [
            'total_tlds' => count($allTlds),
            'country_codes_count' => count($supportedCountryCodes),
            'generic_tlds_count' => count($genericTlds),
        ]);

        return $allTlds;
    }

    /**
     * Execute search with retry logic and exponential backoff.
     */
    protected function executeSearchWithRetry(array $helperConfig, string $engineName, string $query, string $tld, array $params): ?array
    {
        $attempt = 0;
        $lastException = null;

        while ($attempt <= $this->maxRetries) {
            try {
                // For country code TLDs (2 letters), use them for Google domain selection
                // For generic TLDs, use a default country code for API calls
                $countryCodeForApi = $this->getCountryCodeForApi($tld, $helperConfig);

                $results = match ($helperConfig['type']) {
                    'brightdata' => $helperConfig['class']->search($engineName, $query, strtoupper($countryCodeForApi), $params),
                    'serpstack' => $helperConfig['class']->search($query, $params),
                    'serpapi' => $helperConfig['class']->search('google', $query, strtoupper($countryCodeForApi), $params),
                    default => null,
                };

                if ($attempt > 0) {
                    Log::info("[WebSearchShopifyTLDLeadsJob] Search succeeded for {$engineName} TLD {$tld} on attempt " . ($attempt + 1));
                }

                return $results;

            } catch (Exception $e) {
                $lastException = $e;
                $attempt++;

                $isRetryableError = $this->isRetryableError($e);
                $isLastAttempt = $attempt > $this->maxRetries;

                if ($isRetryableError && !$isLastAttempt) {
                    $delay = $this->calculateBackoffDelay($attempt);

                    Log::warning("[WebSearchShopifyTLDLeadsJob] Retryable error on attempt {$attempt} for {$engineName} TLD {$tld}. Retrying in {$delay} seconds.", [
                        'error' => $e->getMessage(),
                        'attempt' => $attempt,
                        'max_retries' => $this->maxRetries,
                        'delay' => $delay,
                    ]);

                    sleep($delay);
                } else {
                    break;
                }
            }
        }

        // Log final failure
        $errorType = $this->categorizeError($lastException);
        Log::error("[WebSearchShopifyTLDLeadsJob] Failed to execute search for {$engineName} TLD {$tld} after " . ($attempt) . " attempts.", [
            'error' => $lastException->getMessage(),
            'error_type' => $errorType,
            'total_attempts' => $attempt,
        ]);

        return null;
    }

    /**
     * Get appropriate country code for API calls.
     * For 2-letter TLDs (country codes), use the TLD itself.
     * For generic TLDs, use a default country code.
     */
    protected function getCountryCodeForApi(string $tld, array $helperConfig): string
    {
        // If it's a 2-letter country code TLD, use it for API calls
        if (strlen($tld) === 2 && ctype_alpha($tld)) {
            return $tld;
        }

        // For generic TLDs, use a default country code for API calls
        // Use 'us' as default since it's widely supported
        return 'us';
    }

    /**
     * Determine if an error is retryable.
     */
    protected function isRetryableError(Exception $e): bool
    {
        $errorMessage = strtolower($e->getMessage());

        // Timeout errors are retryable
        if (str_contains($errorMessage, 'timeout') ||
            str_contains($errorMessage, 'curl error 28') ||
            str_contains($errorMessage, 'operation timed out')) {
            return true;
        }

        // Network connection errors are retryable
        if (str_contains($errorMessage, 'connection') ||
            str_contains($errorMessage, 'network') ||
            str_contains($errorMessage, 'curl error 7')) {
            return true;
        }

        // Server errors (5xx) are retryable
        if (str_contains($errorMessage, '500') ||
            str_contains($errorMessage, '502') ||
            str_contains($errorMessage, '503') ||
            str_contains($errorMessage, '504')) {
            return true;
        }

        // Rate limiting errors are retryable
        if (str_contains($errorMessage, 'rate limit') ||
            str_contains($errorMessage, '429')) {
            return true;
        }

        return false;
    }

    /**
     * Categorize error type for logging.
     */
    protected function categorizeError(?Exception $e): string
    {
        if (!$e) {
            return 'unknown';
        }

        $errorMessage = strtolower($e->getMessage());

        if (str_contains($errorMessage, 'timeout') || str_contains($errorMessage, 'curl error 28')) {
            return 'timeout';
        }

        if (str_contains($errorMessage, 'cc should be a 2-letter country code')) {
            return 'invalid_country_code';
        }

        if (str_contains($errorMessage, 'connection') || str_contains($errorMessage, 'curl error 7')) {
            return 'connection_error';
        }

        if (str_contains($errorMessage, 'rate limit') || str_contains($errorMessage, '429')) {
            return 'rate_limit';
        }

        if (str_contains($errorMessage, '400')) {
            return 'bad_request';
        }

        if (str_contains($errorMessage, '401') || str_contains($errorMessage, '403')) {
            return 'authentication_error';
        }

        if (str_contains($errorMessage, '5')) {
            return 'server_error';
        }

        return 'other';
    }

    /**
     * Calculate exponential backoff delay.
     */
    protected function calculateBackoffDelay(int $attempt): int
    {
        // Exponential backoff: baseDelay * (2 ^ (attempt - 1))
        // With jitter to avoid thundering herd
        $delay = $this->baseDelay * pow(2, $attempt - 1);

        // Add random jitter (±25%)
        $jitter = $delay * 0.25 * (mt_rand(-100, 100) / 100);
        $finalDelay = (int) round($delay + $jitter);

        // Cap at 60 seconds
        return min($finalDelay, 60);
    }

    /**
     * Prepare search parameters by replacing placeholders with actual values.
     *
     * @param  array  $params  The search parameters with placeholders
     * @param  string  $tld  The TLD to use
     * @return array The prepared search parameters
     */
    protected function prepareSearchParams(array $params, string $tld): array
    {
        $countryCodeForApi = $this->getCountryCodeForApi($tld, []);

        return array_map(
            fn ($value) => is_string($value) && $value === '{COUNTRY_CODE}'
                ? strtolower($countryCodeForApi)
                : $value,
            $params
        );
    }

    /**
     * Process the search results and extract domain information.
     *
     * @param  array  $results  The search results
     * @param  string  $engineName  The name of the search engine
     * @return array The processed results with domain information
     */
    protected function processSearchResults(array $results, string $engineName): array
    {
        try {
            $organicResults = $this->extractOrganicResults($results, $engineName);

            if (empty($organicResults)) {
                Log::info("[WebSearchShopifyTLDLeadsJob] No organic results found for {$engineName}.");

                return [];
            }

            $processedResults = array_filter(
                array_map(function ($result) use ($engineName) {
                    $url = $this->extractUrlFromResult($result, $engineName);

                    return empty(trim($url)) ? null : DomainParserHelper::getProcessedDomainInformation($url);
                }, $organicResults)
            );

            Log::info('[WebSearchShopifyTLDLeadsJob] Processed '.count($processedResults)." Shopify store results from {$engineName}.");

            return $processedResults;

        } catch (Exception $e) {
            Log::error("[WebSearchShopifyTLDLeadsJob] Error processing search results from {$engineName}: ".$e->getMessage());

            return [];
        }
    }

    /**
     * Extract organic results from the search results based on the search engine.
     *
     * @param  array  $results  The search results
     * @param  string  $engineName  The name of the search engine
     * @return array The organic results
     */
    protected function extractOrganicResults(array $results, string $engineName): array
    {
        Log::debug("[WebSearchShopifyTLDLeadsJob] Extracting organic results for {$engineName}.", [
            'engine' => $engineName,
            'result_keys' => array_keys($results),
            'result_structure' => $this->getResultStructureDebug($results),
        ]);

        $organicResults = match ($engineName) {
            'bing', 'google' => $results['organic'] ?? [],
            default => [],
        };

        Log::debug("[WebSearchShopifyTLDLeadsJob] Extracted {$engineName} organic results.", [
            'engine' => $engineName,
            'organic_results_count' => count($organicResults),
            'first_result_keys' => ! empty($organicResults) ? array_keys($organicResults[0] ?? []) : [],
        ]);

        return $organicResults;
    }

    /**
     * Extract the URL from a search result based on the search engine.
     *
     * @param  array  $result  The search result
     * @param  string  $engineName  The name of the search engine
     * @return string The extracted URL
     */
    protected function extractUrlFromResult(array $result, string $engineName): string
    {
        Log::debug("[WebSearchShopifyTLDLeadsJob] Extracting URL from {$engineName} result.", [
            'engine' => $engineName,
            'result_keys' => array_keys($result),
            'url_field_link' => $result['link'] ?? 'not_found',
        ]);

        return match ($engineName) {
            'bing', 'google' => $result['link'] ?? '',
            default => '',
        };
    }

    /**
     * Get a debug representation of the result structure.
     *
     * @param  array  $results  The search results
     * @return array Debug information about the structure
     */
    protected function getResultStructureDebug(array $results): array
    {
        return array_map(function ($value, $key) {
            if (is_array($value)) {
                $debug = [
                    'type' => 'array',
                    'count' => count($value),
                    'keys' => ! empty($value) ? array_keys($value) : [],
                ];

                if (isset($value[0]) && is_array($value[0])) {
                    $debug['first_element_keys'] = array_keys($value[0]);
                }

                return $debug;
            }

            return [
                'type' => gettype($value),
                'value' => is_string($value) ? substr($value, 0, 100) : $value,
            ];
        }, $results, array_keys($results));
    }
}
