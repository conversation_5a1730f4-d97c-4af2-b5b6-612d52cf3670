<?php

use App\Jobs\WebSearchShopifyTLDLeadsJob;
use Illuminate\Support\Facades\Queue;

describe('WebSearchShopifyTLDLeadsJob Integration', function () {

    it('can be queued with default parameters', function () {
        Queue::fake();

        WebSearchShopifyTLDLeadsJob::dispatch();

        Queue::assertPushed(WebSearchShopifyTLDLeadsJob::class, 1);
    });

    it('can be queued with custom time range', function () {
        Queue::fake();

        WebSearchShopifyTLDLeadsJob::dispatch('day');

        Queue::assertPushed(WebSearchShopifyTLDLeadsJob::class, 1);
    });

    it('can be queued with custom engines', function () {
        Queue::fake();

        WebSearchShopifyTLDLeadsJob::dispatch('week', ['google']);

        Queue::assertPushed(WebSearchShopifyTLDLeadsJob::class, 1);
    });

    it('supports the requested dispatch patterns', function () {
        Queue::fake();

        // Test the patterns you requested
        WebSearchShopifyTLDLeadsJob::dispatch('week', ['bing', 'google']);
        WebSearchShopifyTLDLeadsJob::dispatch('week');

        Queue::assertPushed(WebSearchShopifyTLDLeadsJob::class, 2);
    });

});
