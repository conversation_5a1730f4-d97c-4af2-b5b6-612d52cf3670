<?php

use App\Jobs\FetchShopifyTLDWebSearchLeadsJob;
use Illuminate\Support\Facades\Queue;

describe('FetchShopifyTLDWebSearchLeadsJob Integration', function () {

    it('can be queued with default parameters', function () {
        Queue::fake();

        FetchShopifyTLDWebSearchLeadsJob::dispatch();

        Queue::assertPushed(FetchShopifyTLDWebSearchLeadsJob::class, 1);
    });

    it('can be queued with custom time range', function () {
        Queue::fake();

        FetchShopifyTLDWebSearchLeadsJob::dispatch('day');

        Queue::assertPushed(FetchShopifyTLDWebSearchLeadsJob::class, 1);
    });

    it('can be queued with custom engines', function () {
        Queue::fake();

        FetchShopifyTLDWebSearchLeadsJob::dispatch('week', ['google']);

        Queue::assertPushed(FetchShopifyTLDWebSearchLeadsJob::class, 1);
    });

    it('supports the requested dispatch patterns', function () {
        Queue::fake();

        // Test the patterns you requested
        FetchShopifyTLDWebSearchLeadsJob::dispatch('week', ['bing', 'google']);
        FetchShopifyTLDWebSearchLeadsJob::dispatch('week');

        Queue::assertPushed(FetchShopifyTLDWebSearchLeadsJob::class, 2);
    });

});
