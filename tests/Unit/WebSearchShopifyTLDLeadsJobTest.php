<?php

use App\Jobs\FetchShopifyTLDWebSearchLeadsJob;

describe('FetchShopifyTLDWebSearchLeadsJob', function () {

    it('can be instantiated with default parameters', function () {
        $job = new FetchShopifyTLDWebSearchLeadsJob;

        expect($job)->toBeInstanceOf(FetchShopifyTLDWebSearchLeadsJob::class);
    });

    it('can be instantiated with time range parameter', function () {
        $job = new FetchShopifyTLDWebSearchLeadsJob('day');

        expect($job)->toBeInstanceOf(FetchShopifyTLDWebSearchLeadsJob::class);
    });

    it('can be instantiated with time range and engines', function () {
        $job = new FetchShopifyTLDWebSearchLeadsJob('week', ['google']);

        expect($job)->toBeInstanceOf(FetchShopifyTLDWebSearchLeadsJob::class);
    });

    it('has default time range of week', function () {
        $job = new FetchShopifyTLDWebSearchLeadsJob;

        $reflection = new ReflectionClass($job);
        $timeRangeProperty = $reflection->getProperty('timeRange');
        $timeRangeProperty->setAccessible(true);

        expect($timeRangeProperty->getValue($job))->toBe('week');
    });

    it('has default search engines', function () {
        $job = new FetchShopifyTLDWebSearchLeadsJob;

        $reflection = new ReflectionClass($job);
        $searchEnginesProperty = $reflection->getProperty('searchEngines');
        $searchEnginesProperty->setAccessible(true);

        expect($searchEnginesProperty->getValue($job))->toBe(['bing', 'google']);
    });

    it('accepts custom time range', function () {
        $job = new FetchShopifyTLDWebSearchLeadsJob('day');

        $reflection = new ReflectionClass($job);
        $timeRangeProperty = $reflection->getProperty('timeRange');
        $timeRangeProperty->setAccessible(true);

        expect($timeRangeProperty->getValue($job))->toBe('day');
    });

    it('accepts custom search engines', function () {
        $job = new FetchShopifyTLDWebSearchLeadsJob('week', ['google']);

        $reflection = new ReflectionClass($job);
        $searchEnginesProperty = $reflection->getProperty('searchEngines');
        $searchEnginesProperty->setAccessible(true);

        expect($searchEnginesProperty->getValue($job))->toBe(['google']);
    });

    it('validates time ranges correctly', function () {
        $validRanges = ['day', 'week', 'month'];

        foreach ($validRanges as $range) {
            $job = new FetchShopifyTLDWebSearchLeadsJob($range);

            $reflection = new ReflectionClass($job);
            $timeRangeProperty = $reflection->getProperty('timeRange');
            $timeRangeProperty->setAccessible(true);

            expect($timeRangeProperty->getValue($job))->toBe($range);
        }
    });

    it('normalizes time range case', function () {
        $job = new FetchShopifyTLDWebSearchLeadsJob('WEEK');

        $reflection = new ReflectionClass($job);
        $timeRangeProperty = $reflection->getProperty('timeRange');
        $timeRangeProperty->setAccessible(true);

        expect($timeRangeProperty->getValue($job))->toBe('week');
    });

    it('can be dispatched with different parameter combinations', function () {
        expect(function () {
            FetchShopifyTLDWebSearchLeadsJob::dispatch();
            FetchShopifyTLDWebSearchLeadsJob::dispatch('week');
            FetchShopifyTLDWebSearchLeadsJob::dispatch('week', ['google']);
            FetchShopifyTLDWebSearchLeadsJob::dispatch('month', ['bing', 'google']);
            FetchShopifyTLDWebSearchLeadsJob::dispatch('week', ['bing', 'google'], 5, 3, 90);
        })->not->toThrow(Exception::class);
    });

    it('has default retry configuration', function () {
        $job = new FetchShopifyTLDWebSearchLeadsJob;

        $reflection = new ReflectionClass($job);

        $maxRetriesProperty = $reflection->getProperty('maxRetries');
        $maxRetriesProperty->setAccessible(true);
        expect($maxRetriesProperty->getValue($job))->toBe(3);

        $baseDelayProperty = $reflection->getProperty('baseDelay');
        $baseDelayProperty->setAccessible(true);
        expect($baseDelayProperty->getValue($job))->toBe(2);

        $requestTimeoutProperty = $reflection->getProperty('requestTimeout');
        $requestTimeoutProperty->setAccessible(true);
        expect($requestTimeoutProperty->getValue($job))->toBe(60);
    });

    it('accepts custom retry configuration', function () {
        $job = new FetchShopifyTLDWebSearchLeadsJob('week', ['google'], 5, 3, 90);

        $reflection = new ReflectionClass($job);

        $maxRetriesProperty = $reflection->getProperty('maxRetries');
        $maxRetriesProperty->setAccessible(true);
        expect($maxRetriesProperty->getValue($job))->toBe(5);

        $baseDelayProperty = $reflection->getProperty('baseDelay');
        $baseDelayProperty->setAccessible(true);
        expect($baseDelayProperty->getValue($job))->toBe(3);

        $requestTimeoutProperty = $reflection->getProperty('requestTimeout');
        $requestTimeoutProperty->setAccessible(true);
        expect($requestTimeoutProperty->getValue($job))->toBe(90);
    });

    it('enforces minimum values for retry configuration', function () {
        $job = new FetchShopifyTLDWebSearchLeadsJob('week', ['google'], -1, 0, 10);

        $reflection = new ReflectionClass($job);

        $maxRetriesProperty = $reflection->getProperty('maxRetries');
        $maxRetriesProperty->setAccessible(true);
        expect($maxRetriesProperty->getValue($job))->toBe(0); // min 0

        $baseDelayProperty = $reflection->getProperty('baseDelay');
        $baseDelayProperty->setAccessible(true);
        expect($baseDelayProperty->getValue($job))->toBe(1); // min 1

        $requestTimeoutProperty = $reflection->getProperty('requestTimeout');
        $requestTimeoutProperty->setAccessible(true);
        expect($requestTimeoutProperty->getValue($job))->toBe(30); // min 30
    });
});
