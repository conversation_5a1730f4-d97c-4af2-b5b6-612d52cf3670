<?php

use App\Jobs\FetchShopifyTLDWebSearchLeadsJob;

it('can create FetchShopifyTLDWebSearchLeadsJob with default parameters', function () {
    $job = new FetchShopifyTLDWebSearchLeadsJob;

    expect($job)->toBeInstanceOf(FetchShopifyTLDWebSearchLeadsJob::class);
});

it('can create FetchShopifyTLDWebSearchLeadsJob with time range', function () {
    $job = new FetchShopifyTLDWebSearchLeadsJob('day');

    expect($job)->toBeInstanceOf(FetchShopifyTLDWebSearchLeadsJob::class);
});

it('can create FetchShopifyTLDWebSearchLeadsJob with time range and engines', function () {
    $job = new FetchShopifyTLDWebSearchLeadsJob('week', ['google']);

    expect($job)->toBeInstanceOf(FetchShopifyTLDWebSearchLeadsJob::class);
});

it('can dispatch FetchShopifyTLDWebSearchLeadsJob', function () {
    expect(function () {
        FetchShopifyTLDWebSearchLeadsJob::dispatch();
    })->not->toThrow(Exception::class);
});

it('can dispatch FetchShopifyTLDWebSearchLeadsJob with time range', function () {
    expect(function () {
        FetchShopifyTLDWebSearchLeadsJob::dispatch('week');
    })->not->toThrow(Exception::class);
});

it('can dispatch FetchShopifyTLDWebSearchLeadsJob with time range and engines', function () {
    expect(function () {
        FetchShopifyTLDWebSearchLeadsJob::dispatch('week', ['google']);
    })->not->toThrow(Exception::class);
});
