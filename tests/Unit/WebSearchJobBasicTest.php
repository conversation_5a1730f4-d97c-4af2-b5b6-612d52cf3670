<?php

use App\Jobs\WebSearchShopifyTLDLeadsJob;

it('can create WebSearchShopifyTLDLeadsJob with default parameters', function () {
    $job = new WebSearchShopifyTLDLeadsJob;

    expect($job)->toBeInstanceOf(WebSearchShopifyTLDLeadsJob::class);
});

it('can create WebSearchShopifyTLDLeadsJob with time range', function () {
    $job = new WebSearchShopifyTLDLeadsJob('day');

    expect($job)->toBeInstanceOf(WebSearchShopifyTLDLeadsJob::class);
});

it('can create WebSearchShopifyTLDLeadsJob with time range and engines', function () {
    $job = new WebSearchShopifyTLDLeadsJob('week', ['google']);

    expect($job)->toBeInstanceOf(WebSearchShopifyTLDLeadsJob::class);
});

it('can dispatch WebSearchShopifyTLDLeadsJob', function () {
    expect(function () {
        WebSearchShopifyTLDLeadsJob::dispatch();
    })->not->toThrow(Exception::class);
});

it('can dispatch WebSearchShopifyTLDLeadsJob with time range', function () {
    expect(function () {
        WebSearchShopifyTLDLeadsJob::dispatch('week');
    })->not->toThrow(Exception::class);
});

it('can dispatch WebSearchShopifyTLDLeadsJob with time range and engines', function () {
    expect(function () {
        WebSearchShopifyTLDLeadsJob::dispatch('week', ['google']);
    })->not->toThrow(Exception::class);
});
