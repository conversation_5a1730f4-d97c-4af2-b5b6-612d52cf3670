<?php

use App\Jobs\FetchShopifyTLDWebSearchLeadsJob;

describe('FetchShopifyTLDWebSearchLeadsJob Retry Logic', function () {

    it('can identify retryable timeout errors', function () {
        $job = new FetchShopifyTLDWebSearchLeadsJob;
        $reflection = new ReflectionClass($job);
        $method = $reflection->getMethod('isRetryableError');
        $method->setAccessible(true);

        $timeoutException = new Exception('cURL error 28: Operation timed out after 30002 milliseconds');
        expect($method->invoke($job, $timeoutException))->toBeTrue();

        $timeoutException2 = new Exception('Request timeout occurred');
        expect($method->invoke($job, $timeoutException2))->toBeTrue();
    });

    it('can identify retryable connection errors', function () {
        $job = new FetchShopifyTLDWebSearchLeadsJob;
        $reflection = new ReflectionClass($job);
        $method = $reflection->getMethod('isRetryableError');
        $method->setAccessible(true);

        $connectionException = new Exception('cURL error 7: Failed to connect');
        expect($method->invoke($job, $connectionException))->toBeTrue();

        $networkException = new Exception('Network connection failed');
        expect($method->invoke($job, $networkException))->toBeTrue();
    });

    it('can identify retryable server errors', function () {
        $job = new FetchShopifyTLDWebSearchLeadsJob;
        $reflection = new ReflectionClass($job);
        $method = $reflection->getMethod('isRetryableError');
        $method->setAccessible(true);

        $serverError500 = new Exception('HTTP 500 Internal Server Error');
        expect($method->invoke($job, $serverError500))->toBeTrue();

        $serverError502 = new Exception('HTTP 502 Bad Gateway');
        expect($method->invoke($job, $serverError502))->toBeTrue();

        $serverError503 = new Exception('HTTP 503 Service Unavailable');
        expect($method->invoke($job, $serverError503))->toBeTrue();

        $serverError504 = new Exception('HTTP 504 Gateway Timeout');
        expect($method->invoke($job, $serverError504))->toBeTrue();
    });

    it('can identify retryable rate limit errors', function () {
        $job = new FetchShopifyTLDWebSearchLeadsJob;
        $reflection = new ReflectionClass($job);
        $method = $reflection->getMethod('isRetryableError');
        $method->setAccessible(true);

        $rateLimitException = new Exception('Rate limit exceeded');
        expect($method->invoke($job, $rateLimitException))->toBeTrue();

        $http429Exception = new Exception('HTTP 429 Too Many Requests');
        expect($method->invoke($job, $http429Exception))->toBeTrue();
    });

    it('can identify non-retryable errors', function () {
        $job = new FetchShopifyTLDWebSearchLeadsJob;
        $reflection = new ReflectionClass($job);
        $method = $reflection->getMethod('isRetryableError');
        $method->setAccessible(true);

        $invalidCountryCode = new Exception('cc should be a 2-letter country code');
        expect($method->invoke($job, $invalidCountryCode))->toBeFalse();

        $authError = new Exception('HTTP 401 Unauthorized');
        expect($method->invoke($job, $authError))->toBeFalse();

        $badRequest = new Exception('HTTP 400 Bad Request');
        expect($method->invoke($job, $badRequest))->toBeFalse();
    });

    it('can categorize error types correctly', function () {
        $job = new FetchShopifyTLDWebSearchLeadsJob;
        $reflection = new ReflectionClass($job);
        $method = $reflection->getMethod('categorizeError');
        $method->setAccessible(true);

        $timeoutError = new Exception('cURL error 28: Operation timed out');
        expect($method->invoke($job, $timeoutError))->toBe('timeout');

        $invalidCountryError = new Exception('cc should be a 2-letter country code');
        expect($method->invoke($job, $invalidCountryError))->toBe('invalid_country_code');

        $connectionError = new Exception('cURL error 7: Failed to connect');
        expect($method->invoke($job, $connectionError))->toBe('connection_error');

        $rateLimitError = new Exception('Rate limit exceeded');
        expect($method->invoke($job, $rateLimitError))->toBe('rate_limit');

        $badRequestError = new Exception('HTTP 400 Bad Request');
        expect($method->invoke($job, $badRequestError))->toBe('bad_request');

        $authError = new Exception('HTTP 401 Unauthorized');
        expect($method->invoke($job, $authError))->toBe('authentication_error');

        $serverError = new Exception('HTTP 500 Internal Server Error');
        expect($method->invoke($job, $serverError))->toBe('server_error');

        $otherError = new Exception('Some other error');
        expect($method->invoke($job, $otherError))->toBe('other');

        expect($method->invoke($job, null))->toBe('unknown');
    });

    it('calculates exponential backoff delay correctly', function () {
        $job = new FetchShopifyTLDWebSearchLeadsJob('week', ['google'], 3, 2, 60);
        $reflection = new ReflectionClass($job);
        $method = $reflection->getMethod('calculateBackoffDelay');
        $method->setAccessible(true);

        // Test that delays increase exponentially (with some tolerance for jitter)
        $delay1 = $method->invoke($job, 1);
        $delay2 = $method->invoke($job, 2);
        $delay3 = $method->invoke($job, 3);

        expect($delay1)->toBeGreaterThan(0);
        expect($delay2)->toBeGreaterThan($delay1);
        expect($delay3)->toBeGreaterThan($delay2);

        // Test that delay is capped at 60 seconds
        $longDelay = $method->invoke($job, 10);
        expect($longDelay)->toBeLessThanOrEqual(60);
    });

    it('can get appropriate country code for API calls', function () {
        $job = new FetchShopifyTLDWebSearchLeadsJob;
        $reflection = new ReflectionClass($job);
        $method = $reflection->getMethod('getCountryCodeForApi');
        $method->setAccessible(true);

        // For 2-letter country code TLDs, should return the TLD itself
        expect($method->invoke($job, 'dk', []))->toBe('dk');
        expect($method->invoke($job, 'se', []))->toBe('se');
        expect($method->invoke($job, 'no', []))->toBe('no');
        expect($method->invoke($job, 'gb', []))->toBe('gb');
        expect($method->invoke($job, 'us', []))->toBe('us');

        // For generic TLDs, should return default 'us'
        expect($method->invoke($job, 'com', []))->toBe('us');
        expect($method->invoke($job, 'store', []))->toBe('us');
        expect($method->invoke($job, 'website', []))->toBe('us');
        expect($method->invoke($job, 'online', []))->toBe('us');

        // Test some of the new generic TLDs
        expect($method->invoke($job, 'jewelry', []))->toBe('us');
        expect($method->invoke($job, 'bike', []))->toBe('us');
        expect($method->invoke($job, 'shop', []))->toBe('us');
        expect($method->invoke($job, 'barcelona', []))->toBe('us');
    });

    it('includes comprehensive generic TLD list', function () {
        $job = new FetchShopifyTLDWebSearchLeadsJob;
        $reflection = new ReflectionClass($job);
        $method = $reflection->getMethod('initializeSearchHelpers');
        $method->setAccessible(true);

        // This test verifies that the job includes a comprehensive list of generic TLDs
        // We can't easily test the private $genericTlds array directly, but we can verify
        // that the job initializes without errors and includes expected TLD types
        expect(function () use ($job, $method) {
            // This should not throw an exception even with no API keys configured
            $method->invoke($job);
        })->not->toThrow(Exception::class);
    });

});
