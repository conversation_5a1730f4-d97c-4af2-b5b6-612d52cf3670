<?php

use App\Console\Commands\FetchLeadsDailyCommand;
use App\Console\Commands\FetchLeadsWeeklyCommand;
use App\Console\Commands\GetLeadData;
use Illuminate\Support\Facades\Schedule;

// Cron
Schedule::command(FetchLeadsDailyCommand::class)->dailyAt('23:00');
Schedule::command(FetchLeadsWeeklyCommand::class)->weeklyOn(0, '23:00');

Schedule::command(GetLeadData::class)->hourlyAt('10');
Schedule::command(GetLeadData::class, ['--tld' => 'dk'])->hourlyAt('50');

// Queue
Schedule::command('queue:retry all')
    ->dailyAt('03:01');

Schedule::command('queue:work --stop-when-empty --timeout=0 --memory=2048')->everyFiveMinutes();
